# 技术上下文

## 技术栈详情

### 后端技术
- **Python 3.x** - 主要开发语言
- **Flask 3.0.2** - Web框架
- **Flask-SQLAlchemy 3.1.1** - ORM工具
- **SQLAlchemy 2.0.40** - 数据库抽象层
- **Werkzeug 3.0.1** - WSGI工具库
- **Jinja2 3.1.3** - 模板引擎

### 前端技术
- **HTML5** - 页面结构
- **CSS3** - 样式设计
- **JavaScript (ES6+)** - 交互逻辑
- **Tailwind CSS** - CSS框架
- **AJAX** - 异步数据交互

### 数据处理
- **markdown 3.5.2** - Markdown解析
- **bleach 6.1.0** - HTML安全清理
- **SQLite** - 本地数据库

### 开发工具
- **Python虚拟环境** - 依赖隔离
- **Git** - 版本控制
- **VS Code** - 代码编辑器

## 开发环境配置

### 环境要求
- Python 3.7或更高版本
- pip包管理器
- 虚拟环境支持

### 依赖安装
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 数据库配置
- **数据库类型**: SQLite
- **数据库文件**: weibo.db（自动创建）
- **表结构**: 
  - posts表（笔记内容、标签、创建时间）
  - users表（用户信息、密码哈希）

### 会话配置
- **会话密钥**: 开发环境默认密钥
- **Cookie命名**: 包含端口信息的动态命名
- **存储方式**: Flask内置会话机制

## 部署配置

### 服务器启动
```bash
# 基本启动
python app.py

# 指定端口启动
python app.py --port 8080

# 环境变量设置端口
PORT=3000 python app.py
```

### 端口配置优先级
1. 命令行参数 `--port`
2. 环境变量 `PORT`
3. 默认端口 `5001`

### 文件上传配置
- **最大文件大小**: 100MB
- **上传目录**: uploads/（自动创建）
- **文件命名**: 时间戳+原文件名

## 工具使用模式

### 开发工具脚本
- `scripts/init_db.py` - 数据库初始化
- `scripts/create_test_user.py` - 创建测试用户
- `scripts/random_weibo.py` - 随机笔记生成
- `scripts/backup.py` - 数据备份
- `scripts/deploy.sh` - 部署脚本
- `scripts/amend.sh` - 提交信息修正
- `scripts/run.sh` - 运行脚本

### Markdown工具
- **渲染扩展**: fenced_code, tables, codehilite, nl2br
- **安全清理**: bleach白名单机制
- **代码高亮**: CSS类支持（无内联样式）

### API设计模式
- **RESTful风格**: 使用HTTP方法表示操作
- **JSON响应**: 统一的API响应格式
- **错误处理**: 标准化的错误信息返回

## 技术约束

### 性能约束
- **单线程会话管理**: 随机浏览功能的会话数据不是线程安全的
- **内存缓存**: 笔记ID列表存储在内存中
- **SQLite限制**: 适合单用户场景，高并发需要考虑其他数据库

### 安全约束
- **XSS防护**: Markdown渲染安全清理
- **SQL注入防护**: SQLAlchemy ORM使用
- **文件上传安全**: 大小限制和文件类型检查
- **密码安全**: Werkzeug哈希存储

### 兼容性约束
- **浏览器兼容**: 现代浏览器支持
- **移动端适配**: 响应式设计
- **操作系统**: 跨平台支持（Windows, macOS, Linux）

## 开发最佳实践

### 代码组织
- **模块化设计**: 功能分离到不同文件
- **模板继承**: 减少重复代码
- **配置管理**: 环境变量和命令行参数支持

### 测试策略
- **手动测试**: 通过浏览器验证功能
- **单元测试**: 需要补充自动化测试
- **集成测试**: API接口测试

### 部署考虑
- **生产环境密钥**: 更换默认secret_key
- **日志记录**: 添加应用日志
- **错误处理**: 完善的异常处理机制
- **备份策略**: 定期数据库备份
