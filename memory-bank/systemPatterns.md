# 系统模式

## 系统架构
采用经典的Flask MVC架构模式：
- **Model** - SQLAlchemy数据模型（Post, User）
- **View** - Jinja2模板引擎渲染HTML
- **Controller** - Flask路由和视图函数处理业务逻辑

### 架构层次
```
┌─────────────────┐
│   前端界面层    │ (HTML/CSS/JavaScript)
└─────────────────┘
        │
┌─────────────────┐
│   控制器层      │ (Flask路由和视图函数)
│   - 路由处理    │
│   - 业务逻辑    │
│   - API接口     │
└─────────────────┘
        │
┌─────────────────┐
│   服务层        │ (工具函数和业务服务)
│   - Markdown处理│
│   - 数据验证    │
│   - 工具函数    │
└─────────────────┘
        │
┌─────────────────┐
│   数据访问层    │ (SQLAlchemy ORM)
│   - 数据模型    │
│   - 数据库操作  │
└─────────────────┘
```

## 关键技术决策

### 1. 数据库设计
- **SQLite本地数据库** - 简化部署，适合单用户场景
- **JSON字段存储标签** - 灵活的标签系统实现
- **UTC时间存储 + 北京时间显示** - 统一时区处理

### 2. 安全策略
- **Markdown安全渲染** - 使用bleach库清理HTML，防止XSS攻击
- **会话管理** - Flask内置会话机制，端口相关cookie命名
- **密码哈希** - Werkzeug安全哈希存储用户密码
- **文件上传限制** - 100MB大小限制，防止恶意上传

### 3. Markdown处理
- **多扩展支持** - 代码块、表格、代码高亮、换行符转换
- **安全清理** - 白名单机制，只允许安全的HTML标签
- **外部链接处理** - 自动添加安全属性
- **实时预览** - AJAX支持的Markdown预览功能

### 4. 标签系统实现
- **JSON数组存储** - 在数据库中以JSON格式存储标签列表
- **SQL查询优化** - 使用SQLite的json_each函数进行标签统计
- **灵活筛选** - 支持按标签筛选和无标签笔记筛选

## 设计模式应用

### 1. 装饰器模式
```python
@login_required  # 身份验证装饰器
```

### 2. 单例模式
```python
markdown_renderer = MarkdownRenderer()  # 全局单例
```

### 3. 工厂模式
```python
db = SQLAlchemy()  # SQLAlchemy工厂模式
```

### 4. 模板模式
```python
# 基础模板 + 继承模板
_base.html → index.html, login.html, etc.
```

## 组件关系

### 前端组件
- **导航栏** - 统一的页面导航（_navbar.html）
- **模态对话框** - 发布和编辑笔记对话框
- **标签系统** - 标签显示和管理组件
- **分页组件** - 笔记列表分页导航

### 后端组件
- **路由系统** - Flask URL路由映射
- **数据模型** - Post和User模型
- **工具模块** - markdown_utils.py
- **API接口** - RESTful风格的JSON API

## 关键实现路径

### 1. 笔记生命周期
```
创建 → 存储 → 渲染 → 展示 → 编辑 → 更新 → 删除
```

### 2. 搜索实现
- 多关键词分词处理
- 内容和标签联合搜索
- SQL LIKE模糊匹配

### 3. 随机浏览
- 会话级笔记ID列表缓存
- 索引管理实现前后导航
- 边界处理和数据同步

### 4. 文件上传
- 大小限制和安全检查
- 时间戳文件名避免冲突
- 本地存储路径管理

## 性能优化策略
- **数据库索引** - 创建时间字段索引优化查询
- **会话缓存** - 随机浏览的ID列表缓存
- **模板继承** - 减少重复代码
- **静态资源** - CSS/JS文件压缩和缓存
