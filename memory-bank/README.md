# Memory Bank - 项目记忆库

## 概述
这是HaoNoteWeb项目的Memory Bank（记忆库），采用Cline Memory Bank规范构建。Memory Bank是一个结构化的文档系统，帮助维护项目上下文，确保在不同的开发会话中能够保持对项目的完整理解。

## 目录结构
```
memory-bank/
├── projectbrief.md     # 项目概述 - 项目的 foundation
├── productContext.md   # 产品上下文 - 项目存在的原因和解决的问题
├── systemPatterns.md   # 系统模式 - 技术架构和设计模式
├── techContext.md      # 技术上下文 - 技术栈和环境配置
├── activeContext.md    # 活动上下文 - 当前工作重点和近期变更
└── progress.md         # 项目进度 - 功能状态和未来规划
```

## 文件说明

### projectbrief.md
项目的 foundation 文档，定义了：
- 项目名称和描述
- 核心目标和主要特性
- 技术定位

### productContext.md
解释项目背景和用户需求：
- 解决的问题
- 用户体验目标
- 功能需求和使用场景

### systemPatterns.md
技术架构和设计决策：
- 系统架构层次
- 关键技术决策
- 设计模式应用
- 组件关系和实现路径

### techContext.md
技术环境和配置信息：
- 技术栈详情
- 开发环境配置
- 部署配置
- 工具使用模式

### activeContext.md
当前活跃的开发信息：
- 当前工作重点
- 最近变更记录
- 下一步计划
- 重要决策和学习洞察

### progress.md
项目进度跟踪：
- 已完成功能
- 待完成功能
- 当前状态评估
- 已知问题和未来规划

## 使用方法

### 对于Cline AI
在每次会话开始时，使用"follow your custom instructions"命令让Cline读取这些文件，重建对项目的理解。

### 对于开发者
- 定期更新相关文件以反映项目变化
- 在功能开发前后更新progress.md
- 重大决策记录在activeContext.md中
- 技术架构变更更新systemPatterns.md

## 维护建议
1. **及时更新** - 在实现重大功能后及时更新相关文档
2. **保持准确** - 确保文档内容与实际代码保持一致
3. **简洁明了** - 使用清晰的结构和易于理解的语言
4. **版本控制** - 通过Git管理Memory Bank文件的变更历史

## 相关资源
- [Cline Memory Bank官方文档](https://cline.bot/blog/memory-bank-how-to-make-cline-an-ai-agent-that-never-forgets)
- 项目主README: ../README.md
- 技术文档: ../docs/ (如果存在)
