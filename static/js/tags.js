/**
 * 标签管理器
 * 用于处理微博发布和编辑时的标签添加、删除等功能
 */
class TagManager {
    /**
     * 创建标签管理器
     * @param {Object} options 配置选项
     * @param {HTMLElement} options.tagInput 标签输入框
     * @param {HTMLElement} options.tagsContainer 标签显示容器
     * @param {HTMLElement} options.tagsField 标签隐藏字段
     */
    constructor(options) {
        this.tagInput = options.tagInput;
        this.tagsContainer = options.tagsContainer;
        this.tagsField = options.tagsField;
        this.tags = options.initialTags || [];
        
        this.init();
    }
    
    /**
     * 初始化标签管理器
     */
    init() {
        // 绑定Enter键添加标签事件
        this.tagInput.addEventListener('keydown', this.handleKeydown.bind(this));
        
        // 绑定标签删除事件
        this.tagsContainer.addEventListener('click', this.handleTagClick.bind(this));
        
        // 更新初始标签显示
        this.updateTags();
    }
    
    /**
     * 处理键盘事件，按Enter添加标签
     * @param {KeyboardEvent} e 键盘事件
     */
    handleKeydown(e) {
        const tag = this.tagInput.value.trim();

        if (e.key === 'Enter') {
            e.preventDefault();
            if (this.addTag(tag)) {
                this.tagInput.value = '';
                this.showFeedback('标签已添加', 'success');
            }
        } else if (e.key === 'Backspace' && !tag && this.tags.length > 0) {
            // 当输入框为空且按退格键时，删除最后一个标签
            e.preventDefault();
            this.removeTag(this.tags.length - 1);
            this.showFeedback('标签已删除', 'info');
        } else if (e.key === 'Escape') {
            // 按ESC清空输入框
            this.tagInput.value = '';
            this.tagInput.blur();
        }
    }
    
    /**
     * 处理标签点击事件，删除标签
     * @param {MouseEvent} e 鼠标事件
     */
    handleTagClick(e) {
        if (e.target.classList.contains('remove-tag')) {
            const index = parseInt(e.target.getAttribute('data-index'));
            this.removeTag(index);
        }
    }
    
    /**
     * 添加标签
     * @param {string} tag 标签文本
     * @returns {boolean} 是否添加成功
     */
    addTag(tag) {
        const validation = this.validateTag(tag);

        if (!validation.valid) {
            this.showFeedback(validation.message, 'warning');
            return false;
        }

        this.tags.push(validation.tag);
        this.updateTags();
        return true;
    }
    
    /**
     * 删除标签
     * @param {number} index 标签索引
     */
    removeTag(index) {
        this.tags.splice(index, 1);
        this.updateTags();
    }
    
    /**
     * 更新标签显示
     */
    updateTags() {
        this.tagsContainer.innerHTML = '';
        this.tags.forEach((tag, index) => {
            const tagElement = document.createElement('span');
            tagElement.className = 'group relative inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium no-underline rounded-full transition-all duration-300 ease-out bg-gradient-to-br from-indigo-50 to-purple-50 text-indigo-600 border border-indigo-200 hover:bg-gradient-to-br hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 hover:border-indigo-300 hover:scale-105 hover:shadow-md hover:shadow-indigo-500/10 hover:-translate-y-0.5';

            // 添加标签文本
            const tagText = document.createElement('span');
            tagText.textContent = tag;
            tagText.className = 'relative z-10';
            tagElement.appendChild(tagText);

            // 添加删除按钮
            const removeButton = document.createElement('span');
            removeButton.className = 'remove-tag flex items-center justify-center w-4 h-4 text-xs font-bold cursor-pointer rounded-full bg-indigo-100 text-indigo-600 hover:bg-indigo-200 transition-all duration-200 hover:scale-110';
            removeButton.setAttribute('data-index', index);
            removeButton.innerHTML = '&times;';
            removeButton.setAttribute('title', `删除标签: ${tag}`);
            removeButton.setAttribute('aria-label', `删除标签: ${tag}`);
            tagElement.appendChild(removeButton);

            // 添加键盘支持
            removeButton.setAttribute('tabindex', '0');
            removeButton.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.removeTag(index);
                }
            });

            this.tagsContainer.appendChild(tagElement);
        });
        this.tagsField.value = JSON.stringify(this.tags);

        // 触发自定义事件，通知标签更新
        const event = new CustomEvent('tagsUpdated', {
            detail: { tags: this.tags }
        });
        this.tagsContainer.dispatchEvent(event);
    }
    
    /**
     * 获取当前标签列表
     * @returns {Array} 标签数组
     */
    getTags() {
        return [...this.tags];
    }
    
    /**
     * 设置标签列表
     * @param {Array} tags 标签数组
     */
    setTags(tags) {
        this.tags = Array.isArray(tags) ? [...tags] : [];
        this.updateTags();
    }
    
    /**
     * 清空标签
     */
    clearTags() {
        this.tags = [];
        this.updateTags();
        this.showFeedback('所有标签已清空', 'info');
    }

    /**
     * 显示用户反馈
     * @param {string} message 反馈消息
     * @param {string} type 反馈类型: success, warning, error, info
     */
    showFeedback(message, type = 'info') {
        // 移除现有的反馈提示
        const existingFeedback = this.tagInput.parentElement.querySelector('.tag-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        // 创建反馈元素
        const feedback = document.createElement('div');
        feedback.className = `tag-feedback absolute -bottom-6 left-0 text-xs px-2 py-1 rounded-md z-10 transition-all duration-300 ${this.getFeedbackClasses(type)}`;
        feedback.textContent = message;

        // 添加到输入框容器
        const container = this.tagInput.parentElement;
        container.style.position = 'relative';
        container.appendChild(feedback);

        // 自动移除反馈
        setTimeout(() => {
            if (feedback.parentElement) {
                feedback.style.opacity = '0';
                feedback.style.transform = 'translateY(-10px)';
                setTimeout(() => feedback.remove(), 300);
            }
        }, 2000);
    }

    /**
     * 获取反馈样式类
     * @param {string} type 反馈类型
     * @returns {string} CSS类名
     */
    getFeedbackClasses(type) {
        const classes = {
            success: 'bg-green-100 text-green-700 border border-green-200',
            warning: 'bg-yellow-100 text-yellow-700 border border-yellow-200',
            error: 'bg-red-100 text-red-700 border border-red-200',
            info: 'bg-blue-100 text-blue-700 border border-blue-200'
        };
        return classes[type] || classes.info;
    }

    /**
     * 验证标签名称
     * @param {string} tag 标签名称
     * @returns {Object} 验证结果
     */
    validateTag(tag) {
        const trimmedTag = tag.trim();

        if (!trimmedTag) {
            return { valid: false, message: '标签名称不能为空' };
        }

        if (trimmedTag.length > 20) {
            return { valid: false, message: '标签名称不能超过20个字符' };
        }

        if (this.tags.includes(trimmedTag)) {
            return { valid: false, message: '标签已存在' };
        }

        if (this.tags.length >= 10) {
            return { valid: false, message: '最多只能添加10个标签' };
        }

        // 检查特殊字符
        if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_]+$/.test(trimmedTag)) {
            return { valid: false, message: '标签只能包含中文、英文、数字、空格、连字符和下划线' };
        }

        return { valid: true, tag: trimmedTag };
    }
    
    /**
     * 检查输入框中未添加的标签并添加
     */
    processRemainingTag() {
        const tag = this.tagInput.value.trim();
        if (tag && !this.tags.includes(tag)) {
            this.addTag(tag);
            this.tagInput.value = '';
            return true;
        }
        return false;
    }
}
