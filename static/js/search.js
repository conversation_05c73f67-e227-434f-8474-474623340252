/**
 * 全局搜索功能模块
 * 支持在导航栏和页面中的搜索功能
 */

(function() {
    'use strict';

    // 搜索功能类
    class SearchManager {
        constructor() {
            this.init();
        }

        init() {
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.setupSearch());
            } else {
                this.setupSearch();
            }
        }

        setupSearch() {
            // 设置导航栏搜索
            this.setupNavbarSearch();
            
            // 设置页面内搜索（如果存在）
            this.setupPageSearch();
            
            // 设置搜索结果高亮
            this.setupSearchHighlight();
        }

        // 设置导航栏搜索功能
        setupNavbarSearch() {
            const navSearchButton = document.getElementById('navSearchButton');
            const navSearchInput = document.getElementById('navSearchInput');

            if (navSearchButton && navSearchInput) {
                // 搜索按钮点击事件
                navSearchButton.addEventListener('click', () => {
                    this.performSearch(navSearchInput.value.trim());
                });

                // 回车键搜索
                navSearchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.performSearch(navSearchInput.value.trim());
                    }
                });
            }
        }

        // 设置页面内搜索功能（兼容原有的搜索框）
        setupPageSearch() {
            const searchButton = document.getElementById('searchButton');
            const searchInput = document.getElementById('searchInput');

            if (searchButton && searchInput) {
                // 搜索按钮点击事件
                searchButton.addEventListener('click', () => {
                    this.performSearch(searchInput.value.trim());
                });

                // 回车键搜索
                searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.performSearch(searchInput.value.trim());
                    }
                });
            }
        }

        // 执行搜索
        performSearch(query) {
            if (query) {
                const searchUrl = this.buildSearchUrl(query);
                window.location.href = searchUrl;
            }
        }

        // 获取当前标签信息
        getCurrentTag() {
            // 检查URL中是否有tag参数
            const urlParams = new URLSearchParams(window.location.search);
            const tagParam = urlParams.get('tag');
            if (tagParam) {
                return tagParam;
            }
            
            // 检查页面上是否显示当前筛选标签
            const currentFilterElement = document.querySelector('.current-filter .current-tag');
            if (currentFilterElement) {
                const tagText = currentFilterElement.textContent.trim();
                if (tagText === '无标签') {
                    return 'no_tags';
                }
                return tagText;
            }
            
            return null;
        }

        // 构建搜索URL，包含当前标签
        buildSearchUrl(query) {
            const currentTag = this.getCurrentTag();
            let url = `/?q=${encodeURIComponent(query)}`;
            if (currentTag) {
                url += `&tag=${encodeURIComponent(currentTag)}`;
            }
            return url;
        }

        // 设置搜索结果高亮
        setupSearchHighlight() {
            const searchQuery = new URLSearchParams(window.location.search).get('q');
            if (searchQuery) {
                const keywords = searchQuery.split(' ').filter(k => k.trim() !== '');
                if (keywords.length > 0) {
                    // 高亮微博正文内容
                    document.querySelectorAll('.post-content').forEach(contentDiv => {
                        this.highlightKeywords(contentDiv, keywords);
                    });
                    // 高亮标签文本
                    document.querySelectorAll('.tags-container .tag').forEach(tagElement => {
                        this.highlightKeywords(tagElement, keywords);
                    });
                }
            }
        }

        // 高亮关键词函数
        highlightKeywords(element, keywords) {
            if (!keywords || keywords.length === 0 || !element) return;

            let innerHTML = element.innerHTML;
            // 避免在高亮标签内部重复高亮
            const marker = "__HIGHLIGHT_MARKER__";
            const highlightedContent = innerHTML.replace(/<span class="highlight">([^<]+)<\/span>/gi, marker + "$1" + marker);

            let tempContent = highlightedContent;
            keywords.forEach(keyword => {
                if (keyword.trim() === '') return; // 跳过空关键词
                // 转义正则表达式特殊字符
                const escapedKeyword = keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, '$&');
                // 创建不区分大小写的正则表达式
                const regex = new RegExp(`(${escapedKeyword})(?![^<>]*>)`, 'gi'); // (?![^<>]*>) 确保不在HTML标签内匹配
                
                // 替换文本节点中的关键词
                tempContent = tempContent.replace(regex, '<span class="highlight">$1</span>');
            });
            
            // 恢复之前的高亮标记
            element.innerHTML = tempContent.replace(new RegExp(marker + "([^" + marker + "]+)" + marker, "g"), '<span class="highlight">$1</span>');
        }
    }

    // 创建全局搜索管理器实例
    window.searchManager = new SearchManager();

})();
