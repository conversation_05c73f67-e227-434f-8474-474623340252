/* 现代化笔记界面样式 */

/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色彩 */
    --primary-color: #667eea;
    --primary-hover: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    
    /* 背景色 */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-card: #ffffff;
    
    /* 文字颜色 */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #a0aec0;
    
    /* 边框和阴影 */
    --border-color: #e2e8f0;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

html {
    overflow-y: scroll; /* 始终显示垂直滚动条，避免页面布局跳动 */
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    font-size: 16px;
}

/* 导航栏样式 */
.nav-container {
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 900px;
    padding: var(--spacing-md) var(--spacing-xl);
    min-height: 60px; /* 确保固定的最小高度 */
    box-sizing: border-box; /* 确保padding计算在高度内 */
}

.nav-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-link {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    text-decoration: none;
    padding: calc(var(--spacing-sm) - 1px) calc(var(--spacing-md) - 1px);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid transparent;
}

.nav-link:hover {
    color: var(--primary-color);
    background-color: rgba(102, 126, 234, 0.1);
    /* 移除transform避免导航栏高度视觉变化 */
}

.nav-link.active {
    color: var(--primary-color);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-color: rgba(102, 126, 234, 0.2); /* 只改变预留边框的颜色 */
}

.nav-button {
    font-size: 14px;
    font-weight: 600;
    color: white;
    background: var(--bg-gradient);
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.nav-button:hover {
    /* 移除transform避免导航栏高度视觉变化 */
    box-shadow: var(--shadow-md);
    opacity: 0.9; /* 使用透明度变化替代位移效果 */
}

.nav-link.logout {
    color: var(--text-muted);
    font-weight: 500;
}

.nav-link.logout:hover {
    color: #ef4444;
    background-color: rgba(239, 68, 68, 0.1);
}

/* 主容器 */
.container {
    max-width: 900px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    margin-top: 80px;
}

/* 搜索容器 */
.search-container {
    margin-bottom: var(--spacing-xl);
    background: var(--bg-card);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.search-input-wrapper {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.search-scope-info {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: var(--spacing-sm);
}

.search-tag {
    color: var(--primary-color);
    font-weight: 600;
}

#searchInput {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 14px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    background: white;
}

#searchInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#searchButton {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 13px;
    font-weight: 600;
    background: var(--bg-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

#searchButton:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 当前筛选标签 */
.current-filter {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    font-weight: 500;
    color: var(--text-primary);
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.current-tag {
    color: var(--primary-color);
    font-weight: 600;
}

.current-filter a {
    color: var(--text-secondary);
    text-decoration: none;
    margin-left: var(--spacing-sm);
    font-weight: 500;
}

.current-filter a:hover {
    color: var(--primary-color);
}

/* 卡片样式 */
.card {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.card-header {
    margin-bottom: 0;
}

.header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.post-time {
    font-size: 13px;
    color: var(--text-muted);
    font-weight: 500;
}

.btn-group {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-secondary,
.btn-danger {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 12px;
    font-weight: 600;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary {
    background: var(--primary-color);
    color: white;
}

.btn-secondary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.post-content {
    font-size: 15px;
    line-height: 1.7;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    word-wrap: break-word;
    white-space: pre-wrap;
}

/* 标签样式 */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.tag {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 12px;
    font-weight: 600;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: 20px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;
}

.tag:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.tag.active {
    background: var(--bg-gradient);
    color: white;
    border-color: transparent;
}

.tag .remove-tag {
    margin-left: var(--spacing-xs);
    font-size: 10px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.tag .remove-tag:hover {
    opacity: 1;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    animation: modalFadeIn 0.3s ease;
}

.modal.show {
    display: block;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--bg-card);
    margin: 5% auto;
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 600px;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease;
    overflow: hidden;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h5 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-button:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* 时间字段容器样式 */
.datetime-field {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.datetime-field input[type="datetime-local"] {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 13px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    background: white;
    color: var(--text-primary);
    font-family: inherit;
}

.datetime-field input[type="datetime-local"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Now按钮样式 */
.btn-now {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 12px;
    font-weight: 600;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: var(--shadow-sm);
}

.btn-now:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-body textarea {
    width: 100%;
    min-height: 200px;
    padding: var(--spacing-lg);
    font-size: 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    font-family: inherit;
    resize: vertical;
    background: #fafafa;
}

.modal-body textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.modal-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: #fafafa;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

.btn-primary {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 14px;
    font-weight: 600;
    background: var(--bg-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 表单样式 */
.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.form-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: var(--spacing-lg);
    gap: var(--spacing-md);
}

.tag-section {
    flex: 1;
    min-width: 0;
}

.tags-input-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
    min-height: 50px;
    transition: all 0.3s ease;
}

.tags-input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.tags-input-wrapper input[type="text"] {
    flex: 1;
    min-width: 120px;
    border: none;
    outline: none;
    padding: var(--spacing-sm);
    font-size: 14px;
    background: transparent;
}

.tags-input-wrapper .tag {
    margin: 0;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
}

.pagination a,
.pagination span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination a {
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    background: white;
}

.pagination a:hover:not(.disabled) {
    background: var(--primary-color);
    color: white;
    /* 移除transform保持整体一致性 */
    box-shadow: var(--shadow-sm);
}

.pagination .active {
    background: var(--bg-gradient);
    color: white;
    border: none;
}

.pagination .disabled {
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-md);
        margin-top: 70px;
    }
    
    .nav-container {
        padding: var(--spacing-sm) var(--spacing-md);
        min-height: 50px; /* 移动端稍小的固定高度 */
    }
    
    .nav-left {
        gap: var(--spacing-md);
    }
    
    .nav-link {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 13px;
    }
    
    .nav-button {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: 13px;
    }
    
    .card {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .header-row {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
    }
    
    .post-time {
        flex: 1;
        min-width: 0;
    }
    
    .btn-group {
        flex-shrink: 0;
        display: flex;
        flex-direction: row;
        gap: var(--spacing-xs);
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
    
    .modal-body {
        padding: var(--spacing-lg);
    }
    
    .form-footer {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }
    
    .tags-input-wrapper {
        min-height: 45px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: var(--spacing-sm);
    }
    
    .card {
        padding: var(--spacing-md);
    }
    
    .post-content {
        font-size: 15px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }
    
    .header-row {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: var(--spacing-xs);
    }
    
    .post-time {
        font-size: 12px;
        flex: 1;
        min-width: 0;
    }
    
    .btn-group {
        flex-direction: row;
        gap: var(--spacing-xs);
        flex-shrink: 0;
    }
    
    .btn-secondary,
    .btn-danger {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 10px;
        min-width: 40px;
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 高亮搜索结果 */
.highlight {
    background: linear-gradient(135deg, rgba(240, 147, 251, 0.3) 0%, rgba(102, 126, 234, 0.3) 100%);
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 600;
}

/* 加载动画 */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

#editTagsContainer {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}
