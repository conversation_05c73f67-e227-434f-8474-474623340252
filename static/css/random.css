/* 随机微博页面样式 */

#post-display-area {
    margin-bottom: var(--spacing-2xl);
}

.post-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
    width: 100%;
    max-width: 700px;
    animation: slideInUp 0.6s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.post-time {
    font-size: 14px;
    color: var(--text-muted);
    font-weight: 500;
}

.post-content {
    font-size: 15px; /* 将字体大小修改为15px */
    color: var(--text-primary);
    margin-bottom: var(--spacing-xl);
    word-wrap: break-word;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.tag {
    background-color: #eaf6fd;
    color: #3498db;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    text-decoration: none;
    transition: background-color 0.2s;
}

.tag:hover {
    background-color: #d0ecf9;
}

.navigation-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-2xl);
}

.nav-btn {
    padding: var(--spacing-sm) var(--spacing-md); /* 减小内边距 */
    font-size: 14px; /* 减小字体大小 */
    font-weight: 600;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 100px; /* 稍微减小最小宽度 */
}

.nav-btn:not(:disabled) {
    background: var(--bg-gradient);
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.nav-btn:disabled {
    background: #f1f3f4;
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.6;
}

#random-again-btn {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    position: relative;
}

#random-again-btn:hover {
    background: linear-gradient(135deg, #e879f9 0%, #ef4444 100%);
}

.btn-group {
    display: flex;
    gap: var(--spacing-sm);
}

#deletePostForm {
    display: inline;
}

@media (max-width: 768px) {
    .post-card {
        padding: var(--spacing-lg);
    }
    
    .post-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .post-time {
        order: 2;
        font-size: 13px;
    }

    .btn-group {
        order: 1;
        width: 100%;
        justify-content: flex-end;
    }

    .post-content {
        font-size: 14px;
    }

    .navigation-buttons {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    #random-again-btn {
        order: -1;
    }
    
    .nav-btn {
        width: 100%;
        padding: var(--spacing-md);
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .post-card {
        padding: var(--spacing-md);
        border-radius: var(--border-radius);
    }

    .post-header {
        gap: var(--spacing-xs);
    }

    .post-time {
        font-size: 12px;
    }

    .post-content {
        font-size: 14px;
    }

    .nav-btn {
        font-size: 14px;
    }

    .btn-group {
        gap: var(--spacing-xs);
    }

    .btn-secondary,
    .btn-danger {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 12px;
    }
}

.loading-post {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    background-color: #f9f9f9;
    border-radius: 8px;
    color: #999;
}

@keyframes shimmer {
    0% { background-position: -468px 0; }
    100% { background-position: 468px 0; }
}

.post-card:has(.post-content:only-child) .post-content {
    text-align: center;
    font-style: italic;
    color: var(--text-muted);
} 