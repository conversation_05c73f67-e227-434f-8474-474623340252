/* 标签页面样式 */

/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色彩 */
    --primary-color: #667eea;
    --primary-hover: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    
    /* 背景色 */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-card: #ffffff;
    
    /* 文字颜色 */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #a0aec0;
    
    /* 边框和阴影 */
    --border-color: #e2e8f0;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    font-size: 16px;
    min-height: 100vh;
}

/* 容器样式 */
.container {
    max-width: 900px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    min-height: 100vh;
}

/* 头部样式 */
h1 {
    margin-bottom: var(--spacing-2xl);
    position: relative;
}

h1 > div {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

/* 标题链接 */
h1 a:first-child {
    font-size: 28px;
    font-weight: 700;
    background: var(--bg-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
    transition: all 0.3s ease;
}

h1 a:first-child:hover {
    transform: translateX(-3px);
}

/* 退出链接 */
h1 a:last-child {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-muted);
    text-decoration: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

h1 a:last-child:hover {
    color: #ef4444;
    background-color: rgba(239, 68, 68, 0.1);
    transform: translateY(-1px);
}

/* 标签列表容器 */
.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 标签卡片样式 */
.tag {
    display: inline-flex;
    align-items: center;
    padding: 6px 14px;
    background: var(--bg-card);
    border-radius: 20px;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.tag::before {
    display: none;
}

.tag:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 特殊标签样式（无标签） */
.special-tag {
    background: rgba(239, 68, 68, 0.05);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #dc2626;
}

.special-tag::before {
    display: none;
}

.special-tag:hover {
    color: #dc2626;
    background: rgba(239, 68, 68, 0.05);
    border-color: #ef4444;
}

/* 标签计数样式 */
.tag-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 22px;
    height: 22px;
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    border-radius: 50%;
    font-size: 12px;
    font-weight: 700;
    margin-left: 8px;
    transition: all 0.3s ease;
}

.special-tag .tag-count {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.tag:hover .tag-count {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.special-tag:hover .tag-count {
    background: #ef4444;
    color: white;
}

/* 空状态 */
.tags-list:empty::after {
    content: '暂无标签';
    display: flex;
    align-items: center;
    justify-content: center;
    grid-column: 1 / -1;
    height: 200px;
    color: var(--text-muted);
    font-size: 18px;
    font-style: italic;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    border: 2px dashed var(--border-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-md);
    }
    
    h1 > div {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
        text-align: center;
    }
    
    h1 a:first-child {
        font-size: 24px;
    }
    
    .tags-list {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: var(--spacing-md);
    }
    
    .tag {
        padding: var(--spacing-md);
        min-height: 70px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: var(--spacing-sm);
    }
    
    h1 > div {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    h1 a:first-child {
        font-size: 20px;
    }
    
    .tags-list {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .tag {
        padding: var(--spacing-md);
        min-height: 60px;
    }
    
    .tag-count {
        min-width: 28px;
        height: 28px;
        font-size: 12px;
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--bg-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 加载动画 */
.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.tag-stats-container {
    margin-top: var(--spacing-xl);
}

.tag-stats-table {
    /* ... existing code ... */
}