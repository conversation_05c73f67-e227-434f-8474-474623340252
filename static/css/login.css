/* 现代化登录页面样式 */

/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色彩 */
    --primary-color: #667eea;
    --primary-hover: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    
    /* 背景色 */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-card: #ffffff;
    
    /* 文字颜色 */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #a0aec0;
    
    /* 边框和阴影 */
    --border-color: #e2e8f0;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-gradient);
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    font-size: 16px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* 背景装饰 */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(240, 147, 251, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(102, 126, 234, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(118, 75, 162, 0.2) 0%, transparent 50%);
    z-index: -1;
}

/* 登录容器 */
.login-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 400px;
    animation: slideIn 0.6s ease;
    position: relative;
    overflow: hidden;
}

/* 登录容器装饰边框 */
.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--bg-gradient);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 标题样式 */
h1 {
    text-align: center;
    color: var(--text-primary);
    font-size: 28px;
    font-weight: 700;
    margin-bottom: var(--spacing-2xl);
    background: var(--bg-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--bg-gradient);
    border-radius: 2px;
}

/* 表单样式 */
form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    position: relative;
}

label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    margin-left: 4px;
    transition: color 0.3s ease;
}

input[type="text"],
input[type="password"] {
    width: 100%;
    padding: var(--spacing-lg);
    font-size: 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
}

input[type="text"]:focus,
input[type="password"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
}

/* 输入框浮动效果 */
.form-group:focus-within label {
    color: var(--primary-color);
    transform: translateX(2px);
}

/* 提交按钮 */
button[type="submit"] {
    width: 100%;
    padding: var(--spacing-lg);
    font-size: 16px;
    font-weight: 600;
    background: var(--bg-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: var(--spacing-md);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

button[type="submit"]:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

button[type="submit"]:active {
    transform: translateY(0);
}

/* 按钮点击波纹效果 */
button[type="submit"]::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

button[type="submit"]:active::after {
    width: 300px;
    height: 300px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    animation: modalFadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 400px;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease;
    overflow: hidden;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(220, 38, 38, 0.05) 100%);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h5 {
    font-size: 18px;
    font-weight: 600;
    color: #dc2626;
    margin: 0;
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-button:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.modal-body {
    padding: var(--spacing-xl);
    text-align: center;
}

.modal-body p {
    color: #dc3545;
    font-weight: 500;
    margin: 0;
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        margin: var(--spacing-md);
        padding: var(--spacing-xl);
        max-width: none;
    }
    
    h1 {
        font-size: 24px;
        margin-bottom: var(--spacing-lg);
    }
    
    input[type="text"],
    input[type="password"],
    button[type="submit"] {
        padding: var(--spacing-md);
        font-size: 15px;
    }
    
    .modal-content {
        margin: var(--spacing-md);
        max-width: none;
    }
    
    .modal-header,
    .modal-body {
        padding: var(--spacing-md);
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(241, 241, 241, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--bg-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 加载状态 */
.loading {
    pointer-events: none;
    opacity: 0.7;
}

.loading button[type="submit"] {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    color: transparent;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}