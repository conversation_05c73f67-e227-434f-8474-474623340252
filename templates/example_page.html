<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示例页面 - 使用发布微博模态框</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: #f8fafc;
            margin: 0;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2d3748;
            margin-bottom: 1rem;
        }
        
        .demo-section {
            margin: 2rem 0;
            padding: 1rem;
            background: #f7fafc;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }
        
        .demo-section h3 {
            margin-top: 0;
            color: #4a5568;
        }
        
        .note {
            background: #fffbf0;
            border: 1px solid #f6e05e;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            color: #744210;
        }
        
        .note strong {
            color: #8b4513;
        }
        
        code {
            background: #e2e8f0;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.9em;
        }
        
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>示例页面 - 复用发布微博模态框</h1>
        
        <div class="demo-section">
            <h3>🎯 使用方法</h3>
            <p>在你的页面中引入发布微博模态框非常简单，只需要包含模板文件即可：</p>
            <pre><code>{% include '_post_modal.html' %}</code></pre>
        </div>
        
        <div class="demo-section">
            <h3>⚙️ 自定义配置</h3>
            <p>你可以通过JavaScript自定义发布成功后的行为：</p>
            <pre><code>// 自定义发布成功后的回调函数
window.onPostCreated = function(post) {
    console.log('新微博发布成功:', post);
    // 在这里添加你的自定义逻辑
    // 例如：更新页面内容、显示通知等
};

// 或者自定义API地址
window.API_URLS = {
    createPost: '/your/custom/create/url'
};</code></pre>
        </div>
        
        <div class="demo-section">
            <h3>🎨 样式自定义</h3>
            <p>模态框包含了完整的CSS样式，不依赖外部样式文件。如需自定义样式，可以在页面中覆盖相应的CSS类。</p>
        </div>
        
        <div class="note">
            <strong>提示：</strong>
            <ul>
                <li>模态框会自动初始化，无需手动调用</li>
                <li>包含完整的标签管理功能</li>
                <li>支持响应式设计，在移动设备上也能良好显示</li>
                <li>默认情况下，发布成功后会刷新页面</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h3>🚀 演示</h3>
            <p>点击下面的按钮测试发布微博功能：</p>
        </div>
    </div>
    
    <!-- 引入发布微博模态框 -->
    {% include '_post_modal.html' %}
    
    <script>
        // 自定义发布成功后的回调
        window.onPostCreated = function(post) {
            alert('微博发布成功！\n\n内容：' + post.content + '\n标签：' + post.tags.join(', '));
            console.log('新发布的微博：', post);
            
            // 这里可以添加你的自定义逻辑
            // 例如：不刷新页面，而是动态更新内容
        };
        
        // 设置API地址（如果需要）
        window.API_URLS = {
            createPost: '{{ url_for("create_post") }}'
        };
    </script>
</body>
</html> 