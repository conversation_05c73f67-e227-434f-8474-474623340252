<!-- 编辑笔记对话框 -->
<div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/70 backdrop-blur-sm animate-fade-in" id="editModal">
    <div class="bg-white my-[2%] mx-auto rounded-2xl w-[90%] min-w-[600px] max-w-[80vw] lg:max-w-[1200px] shadow-2xl animate-slide-in overflow-hidden max-h-[85vh] flex flex-col">
        <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 border-b border-gray-200 flex-shrink-0">
            <div class="flex items-center gap-2">
                <input type="datetime-local" id="editPostTime" name="editPostTime" form="editForm"
                       class="px-2 py-1 text-xs border-2 border-gray-200 rounded-xl transition-all duration-300 bg-white text-gray-800 font-inherit focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100">
                <button type="button" id="setCurrentTimeBtn" class="px-2 py-1 text-xs font-semibold bg-indigo-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 whitespace-nowrap shadow-sm hover:bg-indigo-700 hover:-translate-y-0.5 hover:shadow-md">Now</button>
            </div>
            <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500" data-dismiss="modal">&times;</button>
        </div>
        <div class="p-8 flex-1 overflow-y-auto">
            <form id="editForm" class="space-y-6">
                <div class="flex flex-col gap-2">
                    <!-- Markdown编辑器工具栏 -->
                    <div class="markdown-toolbar">
                        <button type="button" class="toolbar-btn" data-action="bold" title="粗体">
                            <strong>B</strong>
                        </button>
                        <button type="button" class="toolbar-btn" data-action="italic" title="斜体">
                            <em>I</em>
                        </button>
                        <button type="button" class="toolbar-btn" data-action="heading" title="标题">
                            H
                        </button>
                        <button type="button" class="toolbar-btn" data-action="link" title="链接">
                            🔗
                        </button>
                        <button type="button" class="toolbar-btn" data-action="code" title="代码">
                            &lt;/&gt;
                        </button>
                        <button type="button" class="toolbar-btn" data-action="list" title="列表">
                            ≡
                        </button>
                        <div class="toolbar-divider"></div>
                        <button type="button" class="toolbar-btn preview-btn" data-action="preview" title="预览">
                            👁
                        </button>
                    </div>

                    <!-- 编辑器容器 -->
                    <div class="editor-container">
                        <textarea name="content" id="editMarkdownEditor" placeholder="支持Markdown格式，编辑笔记内容" required
                                  class="w-full min-h-250 md:min-h-300 p-6 text-base border-none font-mono resize-y bg-white focus:outline-none"></textarea>
                        <div class="preview-container" id="editPreviewContainer" style="display: none;">
                            <div class="preview-content" id="editPreviewContent"></div>
                        </div>
                    </div>
                </div>
                <!-- 标签编辑区域和保存按钮 -->
                <div class="flex flex-col gap-4">
                    <div class="flex-1 min-w-0">
                        <label for="editTagInput" class="block text-sm font-medium text-gray-700 mb-1.5">
                            编辑标签 <span class="text-gray-400 text-xs">(输入标签名称后按 Enter 键添加，点击标签上的 × 可删除)</span>
                        </label>
                        <div class="group relative flex items-center flex-wrap gap-2 p-3 border-2 border-gray-200 rounded-xl bg-white min-h-[56px] transition-all duration-300 focus-within:border-indigo-500 focus-within:ring-4 focus-within:ring-indigo-100 focus-within:shadow-sm md:min-h-[50px] md:p-2.5">
                            <!-- 标签容器 -->
                            <div class="flex flex-wrap gap-2" id="editTagsContainer"></div>
                            
                            <!-- 标签输入框 -->
                            <input type="text"
                                   id="editTagInput"
                                   placeholder="添加标签..."
                                   class="flex-1 min-w-[120px] border-0 outline-0 p-1 text-sm bg-transparent text-gray-700 placeholder-gray-400 md:min-w-[100px]" />
                            
                            <!-- 输入提示图标 -->
                            <div class="flex-shrink-0 opacity-40 group-focus-within:opacity-60 transition-opacity duration-300">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                            </div>
                            
                            <!-- 保存按钮 -->
                            <div class="flex-shrink-0 ml-2">
                                <button type="submit" form="editForm" class="px-4 py-1.5 bg-indigo-600 text-white font-semibold rounded-lg transition-all duration-300 hover:bg-indigo-700 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-100 text-sm shadow-sm">
                                    保存
                                </button>
                            </div>
                        </div>
                        <input type="hidden" name="tags" id="editTagsField" value="[]" />
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 编辑模态框中的Markdown工具栏样式 */
#editModal .markdown-toolbar {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
    border-radius: 12px 12px 0 0;
    flex-wrap: wrap;
}

#editModal .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    transition: all 0.2s ease;
}

#editModal .toolbar-btn:hover {
    background: #e2e8f0;
    color: #475569;
}

#editModal .toolbar-btn.active {
    background: #6366f1;
    color: white;
}

#editModal .toolbar-divider {
    width: 1px;
    height: 24px;
    background: #e2e8f0;
    margin: 0 4px;
}

#editModal .preview-btn.active {
    background: #10b981;
    color: white;
}



/* 移动端特定样式优化 */
@media (max-width: 768px) {
    #editModal .markdown-toolbar {
        padding: 6px 8px;
        gap: 2px;
    }

    #editModal .toolbar-btn {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    #editModal .toolbar-divider {
        height: 20px;
        margin: 0 2px;
    }
}

@media (max-width: 480px) {
    #editModal .markdown-toolbar {
        padding: 3px 4px;
        gap: 1px;
    }

    #editModal .toolbar-btn {
        width: 26px;
        height: 26px;
        font-size: 11px;
    }

    #editModal .toolbar-divider {
        height: 18px;
        margin: 0 1px;
    }
}

/* 在小屏幕设备上，将标签和按钮垂直排列 */
@media (max-width: 768px) {
    #editModal .flex.items-center.justify-between.gap-4 {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    #editModal .flex-shrink-0 button {
        width: 100%;
        justify-content: center;
    }
}

/* 移动端编辑器内边距优化 */
@media (max-width: 768px) {
    #editModal textarea {
        padding: 1rem !important;
    }
}

@media (max-width: 480px) {
    #editModal textarea {
        padding: 0.75rem !important;
    }
}

/* 编辑器容器样式 */
#editModal .editor-container {
    position: relative;
    border: 2px solid #e2e8f0;
    border-top: none;
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

#editModal .preview-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    overflow-y: auto;
}

#editModal .preview-content {
    padding: 1.5rem;
    line-height: 1.6;
    color: #374151;
}

/* Markdown渲染样式 */
#editModal .preview-content h1, #editModal .preview-content h2, #editModal .preview-content h3,
#editModal .preview-content h4, #editModal .preview-content h5, #editModal .preview-content h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    color: #1f2937;
}

#editModal .preview-content h1 { font-size: 1.5em; }
#editModal .preview-content h2 { font-size: 1.3em; }
#editModal .preview-content h3 { font-size: 1.1em; }

#editModal .preview-content p {
    margin: 8px 0;
}

#editModal .preview-content strong {
    font-weight: 600;
    color: #1f2937;
}

#editModal .preview-content em {
    font-style: italic;
}

#editModal .preview-content code {
    background: #f1f5f9;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #e11d48;
}

#editModal .preview-content pre {
    background: #f8fafc;
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 12px 0;
    border: 1px solid #e2e8f0;
}

#editModal .preview-content pre code {
    background: none;
    padding: 0;
    color: #374151;
}

#editModal .preview-content ul, #editModal .preview-content ol {
    margin: 8px 0;
    padding-left: 24px;
}

#editModal .preview-content li {
    margin: 4px 0;
}

#editModal .preview-content a {
    color: #6366f1;
    text-decoration: underline;
}

#editModal .preview-content a:hover {
    color: #4f46e5;
}

#editModal .preview-content blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 16px;
    margin: 12px 0;
    color: #6b7280;
    font-style: italic;
}
</style>

<script>
// 编辑模态框的Markdown编辑器
class EditMarkdownEditor {
    constructor() {
        this.textarea = document.getElementById('editMarkdownEditor');
        this.previewContainer = document.getElementById('editPreviewContainer');
        this.previewContent = document.getElementById('editPreviewContent');
        this.isPreviewMode = false;

        this.init();
    }

    init() {
        // 绑定工具栏按钮事件
        document.querySelectorAll('#editModal .toolbar-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleToolbarClick(e));
        });
    }

    handleToolbarClick(e) {
        e.preventDefault();
        const action = e.target.closest('.toolbar-btn').getAttribute('data-action');

        switch(action) {
            case 'bold':
                this.insertMarkdown('**', '**', '粗体文本');
                break;
            case 'italic':
                this.insertMarkdown('*', '*', '斜体文本');
                break;
            case 'heading':
                this.insertMarkdown('## ', '', '标题');
                break;
            case 'link':
                this.insertMarkdown('[', '](http://)', '链接文本');
                break;
            case 'code':
                this.insertMarkdown('`', '`', '代码');
                break;
            case 'list':
                this.insertMarkdown('- ', '', '列表项');
                break;
            case 'preview':
                this.togglePreview(e.target.closest('.toolbar-btn'));
                break;
        }
    }

    insertMarkdown(before, after, placeholder) {
        const start = this.textarea.selectionStart;
        const end = this.textarea.selectionEnd;
        const selectedText = this.textarea.value.substring(start, end);
        const text = selectedText || placeholder;

        const newText = before + text + after;
        this.textarea.value = this.textarea.value.substring(0, start) + newText + this.textarea.value.substring(end);

        // 设置光标位置
        const newStart = start + before.length;
        const newEnd = newStart + text.length;
        this.textarea.focus();
        this.textarea.setSelectionRange(newStart, newEnd);
    }

    async togglePreview(button) {
        this.isPreviewMode = !this.isPreviewMode;

        if (this.isPreviewMode) {
            // 显示预览
            button.classList.add('active');
            await this.renderPreview();
            this.previewContainer.style.display = 'block';
        } else {
            // 隐藏预览
            button.classList.remove('active');
            this.previewContainer.style.display = 'none';
        }
    }

    async renderPreview() {
        const content = this.textarea.value;
        try {
            // 发送到后端渲染Markdown
            const response = await fetch('/api/markdown/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({ content: content })
            });

            if (response.ok) {
                const data = await response.json();
                this.previewContent.innerHTML = data.rendered_content;
            } else {
                // 如果API不可用，使用简单的客户端渲染
                this.previewContent.innerHTML = this.simpleMarkdownRender(content);
            }
        } catch (error) {
            // 使用简单的客户端渲染作为后备
            this.previewContent.innerHTML = this.simpleMarkdownRender(content);
        }
    }

    simpleMarkdownRender(content) {
        // 简单的客户端Markdown渲染（后备方案）
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            .replace(/^\- (.*$)/gim, '<li>$1</li>')
            .replace(/\n/g, '<br>');
    }

    setValue(value) {
        this.textarea.value = value;
    }

    getValue() {
        return this.textarea.value;
    }
}

// 移动端虚拟键盘处理
class MobileKeyboardHandler {
    constructor() {
        this.modal = document.getElementById('editModal');
        this.originalViewportHeight = window.innerHeight;
        this.init();
    }

    init() {
        if (!this.modal) return;

        // 监听窗口大小变化（虚拟键盘弹出/收起）
        window.addEventListener('resize', () => this.handleResize());

        // 监听输入框焦点事件
        const inputs = this.modal.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('focus', () => this.handleInputFocus());
            input.addEventListener('blur', () => this.handleInputBlur());
        });
    }

    handleResize() {
        const currentHeight = window.innerHeight;
        const heightDiff = this.originalViewportHeight - currentHeight;

        // 如果高度减少超过150px，认为是虚拟键盘弹出
        if (heightDiff > 150) {
            this.adjustModalForKeyboard(true);
        } else {
            this.adjustModalForKeyboard(false);
        }
    }

    handleInputFocus() {
        // 延迟执行，等待虚拟键盘完全弹出
        setTimeout(() => {
            this.scrollToActiveInput();
        }, 300);
    }

    handleInputBlur() {
        // 虚拟键盘收起时恢复原始状态
        setTimeout(() => {
            this.adjustModalForKeyboard(false);
        }, 300);
    }

    adjustModalForKeyboard(keyboardVisible) {
        const modalContent = this.modal.querySelector('.bg-white');
        if (!modalContent) return;

        if (keyboardVisible) {
            modalContent.style.maxHeight = '70vh';
            modalContent.style.margin = '1% auto';
        } else {
            modalContent.style.maxHeight = '';
            modalContent.style.margin = '';
        }
    }

    scrollToActiveInput() {
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
            activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
}

// 初始化编辑模态框的Markdown编辑器和移动端处理
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('editModal')) {
        window.editMarkdownEditor = new EditMarkdownEditor();
        window.mobileKeyboardHandler = new MobileKeyboardHandler();
    }
});
</script>
