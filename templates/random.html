<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>随机笔记</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <!-- 引入 Tailwind CSS -->
    <link href="/static/css/tailwind/tailwind.min.css" rel="stylesheet">
    <!-- 引入 Tailwind 自定义补充样式 -->
    <link href="/static/css/tailwind-custom.css" rel="stylesheet">
    <!-- 引入随机页面专用样式 -->
    <link href="/static/css/random.css" rel="stylesheet">

    <!-- Markdown内容样式 -->
    <style>
        .markdown-content h1, .markdown-content h2, .markdown-content h3,
        .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            margin: 16px 0 8px 0;
            font-weight: 600;
            color: #1f2937;
        }

        .markdown-content h1 { font-size: 1.5em; }
        .markdown-content h2 { font-size: 1.3em; }
        .markdown-content h3 { font-size: 1.1em; }

        .markdown-content p {
            margin: 8px 0;
        }

        .markdown-content strong {
            font-weight: 600;
            color: #1f2937;
        }

        .markdown-content em {
            font-style: italic;
        }

        .markdown-content code {
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e11d48;
        }

        .markdown-content pre {
            background: #f8fafc;
            padding: 12px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 12px 0;
            border: 1px solid #e2e8f0;
        }

        .markdown-content pre code {
            background: none;
            padding: 0;
            color: #374151;
        }

        .markdown-content ul, .markdown-content ol {
            margin: 8px 0;
            padding-left: 24px;
        }

        .markdown-content ul {
            list-style-type: disc;
        }

        .markdown-content ol {
            list-style-type: decimal;
        }

        .markdown-content li {
            margin: 4px 0;
        }

        .markdown-content a {
            color: #6366f1;
            text-decoration: underline;
        }

        .markdown-content a:hover {
            color: #4f46e5;
        }

        .markdown-content blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 16px;
            margin: 12px 0;
            color: #6b7280;
            font-style: italic;
        }

        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 12px 0;
        }

        .markdown-content th, .markdown-content td {
            border: 1px solid #e5e7eb;
            padding: 8px 12px;
            text-align: left;
        }

        .markdown-content th {
            background: #f8fafc;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- 固定导航栏 -->
    {% set active_page = 'random' %}
    {% include '_navbar.html' %}

    <!-- 主容器 -->
    <div class="max-w-4xl mx-auto px-8 py-8 mt-20 md:px-4 md:mt-16">
        <div id="post-display-area" class="mb-12">
            {% if post %}
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in md:p-6" data-post-id="{{ post.id }}">
                 <div>
                    <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200 md:flex-col md:items-start md:gap-2">
                        <span class="text-sm text-gray-500 font-medium md:order-2 md:text-xs">{{ post.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        <div class="flex gap-2 md:order-1 md:w-full md:justify-end">
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-gray-100 text-gray-600 hover:bg-gray-200 hover:-translate-y-0.5 hover:shadow-sm edit-post" data-post-id="{{ post.id }}">编辑</button>
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-100 text-red-600 hover:bg-red-200 hover:-translate-y-0.5 hover:shadow-sm delete-post" data-post-id="{{ post.id }}">删除</button>
                        </div>
                    </div>
                    <div class="post-content leading-relaxed text-gray-800 mb-8 break-words markdown-content" id="content-{{ post.id }}">{{ post.rendered_content|safe }}</div>
                </div>
                {% if post.tags %}
                <div class="flex flex-wrap gap-2 mt-6 pt-4 border-t border-gray-200">
                    {% for tag in post.tags or [] %} {# 添加 or [] 防止 tags 为 None #}
                    <a href="{{ url_for('index', tag=tag) }}"
                       class="group relative inline-flex items-center px-3 py-1.5 text-xs font-medium no-underline rounded-full transition-all duration-300 ease-out bg-gradient-to-br from-indigo-50 to-purple-50 text-indigo-600 border border-indigo-200 hover:bg-gradient-to-br hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 hover:border-indigo-300 hover:scale-105 hover:shadow-md hover:shadow-indigo-500/10 hover:-translate-y-0.5">
                        <span class="relative z-10">{{ tag }}</span>
                        <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    </a>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% else %}
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in">
                <div class="text-base leading-relaxed text-gray-500 text-center italic">没有找到任何笔记。</div>
            </div>
            {% endif %}
        </div>

        <!-- 导航按钮组 -->
        <div class="flex flex-col gap-3 mt-12 mb-8 max-w-2xl mx-auto md:flex-row md:justify-center md:items-center md:gap-4">
            <!-- 随机一条按钮（主要操作） -->
            <button id="random-again-btn"
                    class="flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl cursor-pointer transition-colors duration-200 w-full bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                           md:min-w-[120px] md:w-auto md:order-2">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                随机一条
            </button>

            <!-- 上一条按钮 -->
            <button id="prev-post-btn"
                    class="flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl cursor-pointer transition-colors duration-200 w-full
                           {% if not post or is_first %}
                           bg-gray-100 text-gray-400 cursor-not-allowed
                           {% else %}
                           bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2
                           {% endif %}
                           md:min-w-[120px] md:w-auto md:order-1"
                    {% if not post or is_first %}disabled{% endif %}>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                上一条
            </button>

            <!-- 下一条按钮 -->
            <button id="next-post-btn"
                    class="flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl cursor-pointer transition-colors duration-200 w-full
                           {% if not post or is_last %}
                           bg-gray-100 text-gray-400 cursor-not-allowed
                           {% else %}
                           bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2
                           {% endif %}
                           md:min-w-[120px] md:w-auto md:order-3"
                    {% if not post or is_last %}disabled{% endif %}>
                下一条
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- 引入编辑笔记对话框 -->
    {% include '_edit_modal.html' %}

    <!-- 删除确认对话框 -->
    <div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/50 backdrop-blur-sm animate-fade-in" id="deleteConfirmModal">
        <div class="bg-white my-[5%] mx-auto rounded-2xl w-[90%] max-w-md shadow-2xl animate-slide-in overflow-hidden">
            <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-red-50/50 to-pink-50/50 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-800 m-0">确认删除</h5>
                <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500" data-dismiss="modal">&times;</button>
            </div>
            <div class="p-8">
                <p class="text-gray-700 text-base m-0">确定要删除这条微博吗？</p>
            </div>
            <div class="px-8 py-6 bg-gray-50 border-t border-gray-200 flex justify-end gap-4">
                <button type="button" class="px-6 py-2 text-sm font-semibold bg-gray-100 text-gray-600 border-0 rounded-xl cursor-pointer transition-all duration-300 hover:bg-gray-200 hover:-translate-y-0.5 hover:shadow-sm" data-dismiss="modal">取消</button>
                <form id="deletePostForm" method="POST" class="inline">
                    <button type="submit" class="px-6 py-2 text-sm font-semibold bg-gradient-to-r from-red-500 to-pink-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 shadow-sm hover:-translate-y-1 hover:shadow-md">删除</button>
                </form>
            </div>
        </div>
    </div>

    <!-- 悬浮发布按钮 -->
    <button id="postButton"
            class="fixed bottom-8 right-8 w-14 h-14 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/30 border-0 cursor-pointer flex items-center justify-center transition-all duration-300 ease-out z-40 hover:shadow-xl hover:shadow-indigo-500/40 hover:scale-110 active:scale-95 md:bottom-6 md:right-6 md:w-12 md:h-12">
        <svg class="w-6 h-6 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
    </button>

    <script>
        window.API_URLS = {
            createPost: '{{ url_for("create_post") }}',
            updatePost: '{{ url_for("update_post", post_id=0) }}'
        };
        
        // 发布按钮点击事件 - 跳转到主页并打开发布模态框
        document.addEventListener('DOMContentLoaded', function() {
            const postButton = document.getElementById('postButton');
            if (postButton) {
                postButton.addEventListener('click', function() {
                    // 跳转到主页并添加参数来指示打开发布模态框
                    window.location.href = '{{ url_for("index") }}?openPost=1';
                });
            }
        });
    </script>
    <script src="/static/js/search.js"></script>
    <script src="/static/js/tags.js"></script>
    <script src="/static/js/random.js"></script>
</body>
</html>
