from datetime import datetime, timedelta
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.types import JSON
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

def beijing_time():
    return datetime.utcnow() + timedelta(hours=8)

class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    tags = db.Column(JSON, default=list)
    created_at = db.Column(db.DateTime, nullable=False, default=beijing_time)

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
