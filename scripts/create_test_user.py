#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import app, db
    from models import User
    from werkzeug.security import generate_password_hash

    def add_test_user():
        with app.app_context():
            # 检查用户是否已存在
            existing_user = User.query.filter_by(username='test').first()
            if existing_user:
                print("测试用户 'test' 已存在。")
                return

            # 创建测试用户
            test_user = User(
                username='test',
                password_hash=generate_password_hash('test123')
            )
            db.session.add(test_user)
            db.session.commit()
            print("测试用户 'test' 已成功添加，密码为 'test123'。")

    if __name__ == '__main__':
        add_test_user()

except Exception as e:
    print(f"创建测试用户时出错: {e}")
    sys.exit(1)
