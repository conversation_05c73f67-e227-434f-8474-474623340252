import os
import shutil
from datetime import datetime

def backup_database():
    # 获取当前脚本所在目录
    base_dir = os.path.abspath(os.path.dirname(__file__))
    # 数据库文件路径
    db_file = os.path.join(os.path.dirname(base_dir), 'weibo.db')
    
    # 确保数据库文件存在
    if not os.path.exists(db_file):
        print('数据库文件不存在')
        return
    
    # 创建备份目录（如果不存在）
    backup_dir = os.path.expanduser('~/Documents/Backup')
    os.makedirs(backup_dir, exist_ok=True)
    
    # 生成带时间戳的备份文件名
    timestamp = datetime.now().strftime('%Y%m%d')
    backup_file = os.path.join(backup_dir, f'weibo_{timestamp}.db')
    
    try:
        # 复制数据库文件到备份目录
        shutil.copy2(db_file, backup_file)
        print(f'数据库已成功备份到: {backup_file}')
    except Exception as e:
        print(f'备份过程中出现错误: {str(e)}')

if __name__ == '__main__':
    backup_database()