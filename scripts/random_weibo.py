import sqlite3
import subprocess
import os
import json

def sync_database():
    try:
        # 从远程服务器同步数据库文件
        subprocess.run(['rsync', '-avz', 'lighthouse@43.134.172.200:~/flask/weibo/weibo.db', '.'], check=True)
        print('数据库同步成功')
    except subprocess.CalledProcessError as e:
        print(f'同步数据库失败：{e}')
        exit(1)

def get_random_note():
    try:
        # 同步数据库
        sync_database()
        
        # 连接到数据库
        conn = sqlite3.connect('weibo.db')
        cursor = conn.cursor()
        
        # 随机获取一行记录及其标签
        cursor.execute('SELECT content, tags FROM post ORDER BY RANDOM() LIMIT 1')
        result = cursor.fetchone()
        
        if result:
            content = result[0]
            tags = json.loads(result[1])
            title = '、'.join(tags) if tags else '无标签'
            # 使用 osascript 显示系统对话框
            apple_script = f'display dialog "{content}" buttons {{"OK"}} default button "OK" with title "{title}"'
            subprocess.run(['osascript', '-e', apple_script])
        else:
            print('数据库中没有记录')
            
    except sqlite3.Error as e:
        print(f'数据库错误：{e}')
    except Exception as e:
        print(f'发生错误：{e}')
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    get_random_note()