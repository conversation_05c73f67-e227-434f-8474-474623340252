#!/bin/bash

# 服务器配置
SERVER_USER="lighthouse"
SERVER_HOST="**************"
SERVER_PORT="22"
REMOTE_DIR="/home/<USER>/flask/weibo"

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查命令是否执行成功
function check_status() {
    if [ $? -eq 0 ]; then
        echo_success "$1"
    else
        echo_error "$2"
        exit 1
    fi
}

# 确保远程目录存在
echo_info "创建远程部署目录..."
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "mkdir -p $REMOTE_DIR"
check_status "远程目录创建成功" "远程目录创建失败"

# 同步项目文件
echo_info "同步项目文件到服务器..."
rsync -avz --exclude '.git' \
    --exclude '__pycache__' \
    --exclude '*.pyc' \
    --exclude 'venv/' \
    --exclude '*.db' \
    --exclude 'node_modules/' \
    -e "ssh -p $SERVER_PORT" \
    ./ $SERVER_USER@$SERVER_HOST:$REMOTE_DIR/
check_status "文件同步成功" "文件同步失败"

# 创建日志目录
echo_info "创建日志目录..."
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "mkdir -p $REMOTE_DIR/logs"
check_status "日志目录创建成功" "日志目录创建失败"

# 在服务器上执行部署命令
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST << ENDSSH
    cd $REMOTE_DIR
    
    # 检查虚拟环境是否存在且有效
    if [ -d "venv" ] && [ -f "venv/bin/python3" ] && venv/bin/python3 --version >/dev/null 2>&1; then
        echo "虚拟环境已存在且有效，跳过重建..."
    else
        echo "虚拟环境不存在或无效，重新创建..."
        # 删除旧的虚拟环境（如果存在）
        if [ -d "venv" ]; then
            echo "删除旧的虚拟环境..."
            rm -rf venv
        fi
        # 创建新的虚拟环境
        echo "创建虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    echo "激活虚拟环境..."
    source venv/bin/activate
    
    # 安装依赖
    echo "安装项目依赖..."
    pip install -r "requirements.txt"
    
    # 检查数据库是否存在
    if [ ! -f "weibo.db" ]; then
        echo "数据库文件不存在，开始初始化数据库..."
        python3 "init_db.py"
    else
        echo "数据库文件已存在，跳过初始化步骤"
    fi
    
    # 退出虚拟环境
    deactivate
    
    # 使用 PM2 管理进程
    if command -v pm2 &> /dev/null; then
        echo "使用 PM2 重启应用服务..."
        # 停止并删除旧实例
        pm2 delete weibo 2>/dev/null || true
        # 使用绝对路径和环境变量启动应用
        pm2 start app.py --name weibo --interpreter $REMOTE_DIR/venv/bin/python3 --env PORT=5001
        pm2 save
        pm2 status weibo
    else
        echo_error "PM2 未安装，请先安装 PM2"
        exit 1
    fi
ENDSSH
check_status "部署完成" "部署失败"

echo_success "=== 部署完成! ==="
echo_info "你可以使用以下命令管理应用："
echo_info "  查看状态: ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST 'pm2 status'"
echo_info "  查看日志: ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST 'pm2 logs weibo'"
echo_info "  重启应用: ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST 'pm2 restart weibo'"
