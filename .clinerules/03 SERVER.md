## Brief overview
此规则文件定义了微博应用的服务器启动配置和运行方式，基于 Flask 框架的应用程序启动规范。

## 服务器启动配置
- 使用 Flask 内置服务器运行应用
- 默认监听地址：127.0.0.1 (本地回环地址)
- 默认端口：5001
- 调试模式：开启 (debug=True)
- 支持通过命令行参数 `--port` 指定端口
- 支持通过环境变量 `PORT` 设置端口
- 端口优先级顺序：命令行参数 > 环境变量 > 默认端口

## 启动命令格式
- 基本启动：`python app.py`
- 指定端口启动：`python app.py --port 5001`
- 环境变量启动：`PORT=5001 python app.py`

## 数据库配置
- 使用 SQLite 数据库
- 数据库文件：weibo.db (位于应用根目录)
- 数据库在应用启动时自动初始化创建

## 会话配置
- 会话密钥已配置
- Session cookie 名称包含端口信息以支持多实例运行
- 最大上传大小限制：100MB

## 开发环境要求
- Python 3.x 环境
- 安装 requirements.txt 中的所有依赖
- 确保 uploads 目录存在且可写
